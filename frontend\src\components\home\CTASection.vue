<template>
  <section class="cta-section">
    <div class="cta-background">
      <div class="bg-gradient"></div>
      <div class="bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <el-container class="cta-container">
      <div class="cta-content">
        <!-- 主要CTA -->
        <div class="main-cta">
          <div class="cta-icon">
            <el-icon size="60"><Rocket /></el-icon>
          </div>
          
          <h2 class="cta-title">{{ $t('home.cta.title') }}</h2>
          <p class="cta-subtitle">{{ $t('home.cta.subtitle') }}</p>
          
          <div class="cta-actions">
            <el-button
              type="primary"
              size="large"
              round
              class="cta-primary"
              @click="handleGetStarted"
            >
              <el-icon><CaretRight /></el-icon>
              {{ $t('home.cta.getStarted') }}
            </el-button>
            
            <el-button
              size="large"
              round
              class="cta-secondary"
              @click="handleContactUs"
            >
              <el-icon><ChatDotRound /></el-icon>
              {{ $t('home.cta.contactUs') }}
            </el-button>
          </div>
        </div>

        <!-- 特色亮点 -->
        <div class="cta-highlights">
          <div 
            v-for="highlight in highlights" 
            :key="highlight.id"
            class="highlight-item"
          >
            <div class="highlight-icon">
              <el-icon><component :is="highlight.icon" /></el-icon>
            </div>
            <div class="highlight-content">
              <h4 class="highlight-title">{{ highlight.title }}</h4>
              <p class="highlight-description">{{ highlight.description }}</p>
            </div>
          </div>
        </div>

        <!-- 用户评价 -->
        <div class="testimonials">
          <div class="testimonial-header">
            <h3>{{ $t('home.testimonials.title') }}</h3>
          </div>
          
          <div class="testimonials-grid">
            <div 
              v-for="testimonial in testimonials" 
              :key="testimonial.id"
              class="testimonial-card"
            >
              <div class="testimonial-content">
                <div class="testimonial-quote">
                  <el-icon><ChatLineRound /></el-icon>
                </div>
                <p class="testimonial-text">{{ testimonial.text }}</p>
              </div>
              
              <div class="testimonial-author">
                <div class="author-avatar">
                  <el-avatar :size="40">{{ testimonial.author.charAt(0) }}</el-avatar>
                </div>
                <div class="author-info">
                  <div class="author-name">{{ testimonial.author }}</div>
                  <div class="author-role">{{ testimonial.role }}</div>
                </div>
                <div class="testimonial-rating">
                  <el-icon v-for="i in 5" :key="i" class="star-icon">
                    <Star />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-container>
  </section>
</template>

<script>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import {
  Promotion,
  CaretRight,
  ChatDotRound,
  Lightning,
  Shield,
  Service,
  ChatLineRound,
  Star
} from '@element-plus/icons-vue'

export default {
  name: 'CTASection',
  components: {
    Promotion,
    CaretRight,
    ChatDotRound,
    Lightning,
    Shield,
    Service,
    ChatLineRound,
    Star
  },
  setup() {
    const { t } = useI18n()
    const router = useRouter()

    const highlights = computed(() => [
      {
        id: 'fast',
        title: t('home.cta.highlights.fast.title'),
        description: t('home.cta.highlights.fast.description'),
        icon: Lightning
      },
      {
        id: 'secure',
        title: t('home.cta.highlights.secure.title'),
        description: t('home.cta.highlights.secure.description'),
        icon: Shield
      },
      {
        id: 'support',
        title: t('home.cta.highlights.support.title'),
        description: t('home.cta.highlights.support.description'),
        icon: Service
      }
    ])

    const testimonials = computed(() => [
      {
        id: 1,
        text: t('home.testimonials.testimonial1.text'),
        author: t('home.testimonials.testimonial1.author'),
        role: t('home.testimonials.testimonial1.role')
      },
      {
        id: 2,
        text: t('home.testimonials.testimonial2.text'),
        author: t('home.testimonials.testimonial2.author'),
        role: t('home.testimonials.testimonial2.role')
      },
      {
        id: 3,
        text: t('home.testimonials.testimonial3.text'),
        author: t('home.testimonials.testimonial3.author'),
        role: t('home.testimonials.testimonial3.role')
      }
    ])

    const handleGetStarted = () => {
      router.push('/register')
    }

    const handleContactUs = () => {
      // 可以打开联系我们的模态框或跳转到联系页面
      console.log('Contact us clicked')
    }

    return {
      highlights,
      testimonials,
      handleGetStarted,
      handleContactUs
    }
  }
}
</script>

<style lang="scss" scoped>
.cta-section {
  position: relative;
  padding: 100px 0;
  background: #f8f9fa;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;

  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.05;

    .shape {
      position: absolute;
      background: var(--el-color-primary);
      border-radius: 50%;

      &.shape-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        right: 10%;
        animation: float 6s ease-in-out infinite;
      }

      &.shape-2 {
        width: 150px;
        height: 150px;
        bottom: 20%;
        left: 5%;
        animation: float 8s ease-in-out infinite reverse;
      }

      &.shape-3 {
        width: 100px;
        height: 100px;
        top: 60%;
        right: 20%;
        animation: float 10s ease-in-out infinite;
      }
    }
  }
}

.cta-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.main-cta {
  text-align: center;
  margin-bottom: 80px;
}

.cta-icon {
  color: var(--el-color-primary);
  margin-bottom: 24px;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 28px;
  }
}

.cta-subtitle {
  font-size: 18px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 0 0 32px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary {
    background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-dark-2));
    border: none;
    padding: 12px 32px;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(64, 158, 255, 0.4);
    }
  }

  .cta-secondary {
    background: white;
    border: 1px solid var(--el-border-color);
    color: var(--el-text-color-primary);

    &:hover {
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      transform: translateY(-2px);
    }
  }
}

.cta-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 80px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

.highlight-icon {
  width: 48px;
  height: 48px;
  background: var(--el-color-primary-light-9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-color-primary);
  font-size: 20px;
  flex-shrink: 0;
}

.highlight-content {
  flex: 1;
}

.highlight-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.highlight-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  margin: 0;
}

.testimonials {
  text-align: center;
}

.testimonial-header {
  margin-bottom: 40px;

  h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.testimonial-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--el-border-color-lighter);
  text-align: left;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

.testimonial-content {
  margin-bottom: 16px;
}

.testimonial-quote {
  color: var(--el-color-primary);
  font-size: 24px;
  margin-bottom: 12px;
}

.testimonial-text {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin: 0;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.author-role {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.testimonial-rating {
  display: flex;
  gap: 2px;

  .star-icon {
    color: #ffd700;
    font-size: 14px;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .cta-section {
    padding: 60px 0;
  }

  .main-cta {
    margin-bottom: 60px;
  }

  .cta-highlights {
    margin-bottom: 60px;
  }
}
</style>
